using System;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Microsoft.Win32;

namespace CoreManager
{
    public partial class MainForm : Form
    {
        private Process commandProcess;
        private string fullCommand = "";
        private bool isRunning = false;
        private NotifyIcon trayIcon;
        private ContextMenuStrip trayMenu;
        private const int MaxLogLines = 20;  // 最多显示20行日志

        public MainForm()
        {
            InitializeComponent();
            InitializeTrayIcon();
            LoadSettings();
            
            // 检查是否是开机启动
            string[] args = Environment.GetCommandLineArgs();
            if (args.Length > 1 && args[1] == "/startup")
            {
                this.WindowState = FormWindowState.Minimized;
                this.ShowInTaskbar = false;
                this.Hide();
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 窗体设置 - 固定大小
            this.Text = "命令行程序管理器";
            this.Size = new System.Drawing.Size(720, 580);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle; // 固定边框样式
            this.MaximizeBox = false; // 禁用最大化按钮
            this.MinimizeBox = true;  // 保留最小化按钮
            this.AutoScaleMode = AutoScaleMode.Font; // 使用字体缩放模式
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            
            // 运行命令输入
            var lblCommand = new Label();
            lblCommand.Text = "运行命令:";
            lblCommand.Location = new System.Drawing.Point(20, 20);
            lblCommand.Size = new System.Drawing.Size(80, 23);
            lblCommand.AutoSize = true;
            this.Controls.Add(lblCommand);

            var txtCommand = new TextBox();
            txtCommand.Name = "txtCommand";
            txtCommand.Location = new System.Drawing.Point(20, 45);
            txtCommand.Size = new System.Drawing.Size(680, 23);
            txtCommand.TextChanged += TxtCommand_TextChanged;
            this.Controls.Add(txtCommand);

            // 添加示例说明
            var lblExample = new Label();
            lblExample.Text = "例如: C:\\xray\\xray.exe run -c C:\\xray\\config.json";
            lblExample.Location = new System.Drawing.Point(20, 75);
            lblExample.Size = new System.Drawing.Size(680, 20);
            lblExample.ForeColor = System.Drawing.Color.Gray;
            lblExample.Font = new System.Drawing.Font(lblExample.Font, System.Drawing.FontStyle.Italic);
            this.Controls.Add(lblExample);

            // 控制按钮
            var btnStart = new Button();
            btnStart.Name = "btnStart";
            btnStart.Text = "启动程序";
            btnStart.Location = new System.Drawing.Point(20, 110);
            btnStart.Size = new System.Drawing.Size(120, 40);
            btnStart.Click += BtnStart_Click;
            this.Controls.Add(btnStart);

            var btnStop = new Button();
            btnStop.Name = "btnStop";
            btnStop.Text = "停止程序";
            btnStop.Location = new System.Drawing.Point(160, 110);
            btnStop.Size = new System.Drawing.Size(120, 40);
            btnStop.Enabled = false;
            btnStop.Click += BtnStop_Click;
            this.Controls.Add(btnStop);

            var btnOpenDir = new Button();
            btnOpenDir.Text = "打开目录";
            btnOpenDir.Location = new System.Drawing.Point(300, 110);
            btnOpenDir.Size = new System.Drawing.Size(120, 40);
            btnOpenDir.Click += BtnOpenDir_Click;
            this.Controls.Add(btnOpenDir);

            // 开机启动
            var chkAutoStart = new CheckBox();
            chkAutoStart.Name = "chkAutoStart";
            chkAutoStart.Text = "开机自动启动此程序";
            chkAutoStart.Location = new System.Drawing.Point(20, 170);
            chkAutoStart.Size = new System.Drawing.Size(200, 25);
            chkAutoStart.CheckedChanged += ChkAutoStart_CheckedChanged;
            this.Controls.Add(chkAutoStart);

            // 状态显示
            var lblStatus = new Label();
            lblStatus.Text = "状态:";
            lblStatus.Location = new System.Drawing.Point(20, 210);
            lblStatus.Size = new System.Drawing.Size(50, 23);
            this.Controls.Add(lblStatus);

            var lblStatusValue = new Label();
            lblStatusValue.Name = "lblStatusValue";
            lblStatusValue.Text = "未运行";
            lblStatusValue.Location = new System.Drawing.Point(75, 210);
            lblStatusValue.Size = new System.Drawing.Size(100, 23);
            lblStatusValue.ForeColor = System.Drawing.Color.Red;
            this.Controls.Add(lblStatusValue);

            // 输出日志标签
            var lblOutput = new Label();
            lblOutput.Text = "输出日志:";
            lblOutput.Location = new System.Drawing.Point(20, 245);
            lblOutput.Size = new System.Drawing.Size(80, 23);
            this.Controls.Add(lblOutput);

            // 输出日志
            var txtOutput = new TextBox();
            txtOutput.Name = "txtOutput";
            txtOutput.Location = new System.Drawing.Point(20, 270);
            txtOutput.Size = new System.Drawing.Size(680, 280);
            txtOutput.Multiline = true;
            txtOutput.ScrollBars = ScrollBars.None;  // 移除滚动条
            txtOutput.ReadOnly = true;
            txtOutput.BackColor = System.Drawing.Color.Black;
            txtOutput.ForeColor = System.Drawing.Color.LightGreen;
            txtOutput.Font = new System.Drawing.Font("Consolas", 9F);
            txtOutput.WordWrap = true;  // 启用自动换行
            this.Controls.Add(txtOutput);

            this.ResumeLayout(false);
        }
        
        private void InitializeTrayIcon()
        {
            // 创建托盘图标
            trayIcon = new NotifyIcon();
            trayIcon.Text = "命令行程序管理器";
            
            // 创建一个简单的图标（你可以替换为自定义图标）
            using (var bmp = new Bitmap(16, 16))
            {
                using (var g = Graphics.FromImage(bmp))
                {
                    g.Clear(Color.Blue);
                    g.FillEllipse(Brushes.White, 2, 2, 12, 12);
                }
                trayIcon.Icon = Icon.FromHandle(bmp.GetHicon());
            }
            
            // 创建托盘菜单
            trayMenu = new ContextMenuStrip();
            trayMenu.Items.Add("显示窗口", null, (s, e) => ShowMainWindow());
            trayMenu.Items.Add("启动程序", null, (s, e) => BtnStart_Click(null, null));
            trayMenu.Items.Add("停止程序", null, (s, e) => BtnStop_Click(null, null));
            trayMenu.Items.Add("-");
            trayMenu.Items.Add("退出", null, (s, e) => ExitApplication());
            
            trayIcon.ContextMenuStrip = trayMenu;
            
            // 双击托盘图标显示窗口
            trayIcon.DoubleClick += (s, e) => ShowMainWindow();
            
            // 显示托盘图标
            trayIcon.Visible = true;
        }
        
        private void ShowMainWindow()
        {
            this.Show();
            this.WindowState = FormWindowState.Normal;
            this.ShowInTaskbar = true;
            this.Activate();
        }
        
        private void ExitApplication()
        {
            // 停止正在运行的程序
            if (isRunning)
            {
                BtnStop_Click(null, null);
            }
            
            // 隐藏托盘图标
            if (trayIcon != null)
            {
                trayIcon.Visible = false;
                trayIcon.Dispose();
            }
            
            // 退出应用程序
            Application.Exit();
        }

        private void TxtCommand_TextChanged(object sender, EventArgs e)
        {
            var txtCommand = sender as TextBox;
            fullCommand = txtCommand.Text.Trim();
            SaveSettings();
        }

        private void BtnStart_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(fullCommand))
            {
                MessageBox.Show("请先输入运行命令！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                // 解析命令和参数
                string fileName;
                string arguments;
                string workingDirectory;
                
                if (!ParseCommand(fullCommand, out fileName, out arguments, out workingDirectory))
                {
                    MessageBox.Show("命令格式无效！请检查输入的命令。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                
                commandProcess = new Process();
                commandProcess.StartInfo.FileName = fileName;
                commandProcess.StartInfo.Arguments = arguments;
                commandProcess.StartInfo.WorkingDirectory = workingDirectory;
                commandProcess.StartInfo.UseShellExecute = false;
                commandProcess.StartInfo.RedirectStandardOutput = true;
                commandProcess.StartInfo.RedirectStandardError = true;
                commandProcess.StartInfo.CreateNoWindow = true;
                
                // 设置UTF-8编码
                commandProcess.StartInfo.StandardOutputEncoding = Encoding.UTF8;
                commandProcess.StartInfo.StandardErrorEncoding = Encoding.UTF8;
                
                commandProcess.OutputDataReceived += (s, args) => {
                    if (!string.IsNullOrEmpty(args.Data))
                        LogMessage(args.Data);
                };
                
                commandProcess.ErrorDataReceived += (s, args) => {
                    if (!string.IsNullOrEmpty(args.Data))
                        LogMessage($"错误: {args.Data}");
                };

                commandProcess.Start();
                commandProcess.BeginOutputReadLine();
                commandProcess.BeginErrorReadLine();
                
                isRunning = true;
                UpdateUI();
                LogMessage($"程序已启动: {fullCommand}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ParseCommand(string command, out string fileName, out string arguments, out string workingDirectory)
        {
            fileName = "";
            arguments = "";
            workingDirectory = "";

            try
            {
                command = command.Trim();
                if (string.IsNullOrEmpty(command))
                    return false;

                // 处理带引号的路径
                if (command.StartsWith("\""))
                {
                    int endQuote = command.IndexOf("\"", 1);
                    if (endQuote > 0)
                    {
                        fileName = command.Substring(1, endQuote - 1);
                        arguments = command.Substring(endQuote + 1).Trim();
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    // 查找第一个空格作为分隔符
                    int spaceIndex = command.IndexOf(' ');
                    if (spaceIndex > 0)
                    {
                        fileName = command.Substring(0, spaceIndex);
                        arguments = command.Substring(spaceIndex + 1).Trim();
                    }
                    else
                    {
                        fileName = command;
                        arguments = "";
                    }
                }

                // 检查文件是否存在
                if (!File.Exists(fileName))
                {
                    return false;
                }

                // 设置工作目录为程序所在目录
                workingDirectory = Path.GetDirectoryName(fileName);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private void BtnStop_Click(object sender, EventArgs e)
        {
            try
            {
                if (commandProcess != null && !commandProcess.HasExited)
                {
                    commandProcess.Kill();
                    commandProcess.WaitForExit(5000);
                }
                
                isRunning = false;
                UpdateUI();
                LogMessage("程序已停止");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"停止失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnOpenDir_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(fullCommand))
            {
                MessageBox.Show("请先输入运行命令！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                string fileName, arguments, workingDirectory;
                if (ParseCommand(fullCommand, out fileName, out arguments, out workingDirectory))
                {
                    if (Directory.Exists(workingDirectory))
                    {
                        Process.Start("explorer.exe", workingDirectory);
                    }
                    else
                    {
                        MessageBox.Show("程序目录不存在！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    MessageBox.Show("无法解析命令路径！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开目录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ChkAutoStart_CheckedChanged(object sender, EventArgs e)
        {
            var chkAutoStart = sender as CheckBox;
            SetAutoStart(chkAutoStart.Checked);
        }

        private void SetAutoStart(bool enable)
        {
            try
            {
                string appName = "CoreManager";
                string appPath = Application.ExecutablePath;
                
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Run", true))
                {
                if (enable)
                {
                    // 添加/startup参数，使程序开机启动时最小化到托盘
                    key.SetValue(appName, $"\"{appPath}\" /startup");
                    LogMessage("已设置开机自启动");
                }
                    else
                    {
                        key.DeleteValue(appName, false);
                        LogMessage("已取消开机自启动");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置开机启动失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateUI()
        {
            var btnStart = this.Controls["btnStart"] as Button;
            var btnStop = this.Controls["btnStop"] as Button;
            var lblStatusValue = this.Controls["lblStatusValue"] as Label;

            btnStart.Enabled = !isRunning;
            btnStop.Enabled = isRunning;
            
            // 更新托盘菜单项状态
            if (trayMenu != null && trayMenu.Items.Count >= 3)
            {
                trayMenu.Items[1].Enabled = !isRunning; // 启动程序
                trayMenu.Items[2].Enabled = isRunning;  // 停止程序
            }
            
            if (isRunning)
            {
                lblStatusValue.Text = "运行中";
                lblStatusValue.ForeColor = System.Drawing.Color.Green;
                if (trayIcon != null)
                    trayIcon.Text = "命令行程序管理器 - 运行中";
            }
            else
            {
                lblStatusValue.Text = "未运行";
                lblStatusValue.ForeColor = System.Drawing.Color.Red;
                if (trayIcon != null)
                    trayIcon.Text = "命令行程序管理器 - 未运行";
            }
        }

        private void LogMessage(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(LogMessage), message);
                return;
            }

            var txtOutput = this.Controls["txtOutput"] as TextBox;
            
            // 添加新的日志行
            string newLine = $"[{DateTime.Now:HH:mm:ss}] {message}";
            
            // 获取现有的所有行
            string[] lines = txtOutput.Text.Split(new[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);
            
            // 如果行数超过最大限制，保留最新的行
            if (lines.Length >= MaxLogLines)
            {
                // 保留最后 MaxLogLines-1 行，然后添加新行
                lines = lines.Skip(lines.Length - MaxLogLines + 1).ToArray();
            }
            
            // 重建文本内容
            txtOutput.Text = string.Join("\r\n", lines) + (lines.Length > 0 ? "\r\n" : "") + newLine + "\r\n";
            
            // 将光标移动到最后
            txtOutput.SelectionStart = txtOutput.Text.Length;
            txtOutput.ScrollToCaret();
        }

        private void SaveSettings()
        {
            try
            {
                string settingsPath = Path.Combine(Application.StartupPath, "settings.txt");
                File.WriteAllText(settingsPath, fullCommand, Encoding.UTF8);
            }
            catch { }
        }

        private void LoadSettings()
        {
            try
            {
                string settingsPath = Path.Combine(Application.StartupPath, "settings.txt");
                if (File.Exists(settingsPath))
                {
                    fullCommand = File.ReadAllText(settingsPath, Encoding.UTF8).Trim();
                    
                    var txtCommand = this.Controls["txtCommand"] as TextBox;
                    if (txtCommand != null)
                    {
                        txtCommand.Text = fullCommand;
                    }
                }

                // 检查开机启动状态
                CheckAutoStartStatus();
            }
            catch { }
        }

        private void CheckAutoStartStatus()
        {
            try
            {
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"))
                {
                    var chkAutoStart = this.Controls["chkAutoStart"] as CheckBox;
                    chkAutoStart.Checked = key.GetValue("CoreManager") != null;
                }
            }
            catch { }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // 如果是点击关闭按钮，最小化到托盘而不是退出
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                this.WindowState = FormWindowState.Minimized;
                this.ShowInTaskbar = false;
                this.Hide();
                
                // 显示托盘提示
                if (trayIcon != null)
                {
                    trayIcon.ShowBalloonTip(2000, "命令行程序管理器", 
                        "程序已最小化到系统托盘", ToolTipIcon.Info);
                }
                return;
            }
            
            SaveSettings();
            base.OnFormClosing(e);
        }
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (trayIcon != null)
                {
                    trayIcon.Visible = false;
                    trayIcon.Dispose();
                }
                if (trayMenu != null)
                {
                    trayMenu.Dispose();
                }
            }
            base.Dispose(disposing);
        }
    }
}