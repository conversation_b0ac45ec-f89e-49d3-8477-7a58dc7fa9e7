# 命令行程序管理器

一个用于管理持续运行的命令行程序的Windows窗体应用程序，支持Windows 7、10、11系统。

## 功能特性

- **程序选择**: 通过文件浏览器选择要管理的命令行程序
- **一键启动/停止**: 方便地启动和停止目标程序
- **参数配置**: 支持为命令行程序设置启动参数
- **开机自启动**: 可设置管理器随系统启动
- **目录快速访问**: 一键打开程序所在目录
- **实时日志**: 显示程序的输出和错误信息
- **状态监控**: 实时显示程序运行状态

## 系统要求

- Windows 7 SP1 或更高版本
- .NET Framework 4.8

## 使用方法

1. **选择程序**: 点击"浏览"按钮选择要管理的.exe文件
2. **设置参数**: 在"启动参数"框中输入命令行参数（可选）
3. **启动程序**: 点击"启动程序"按钮运行目标程序
4. **查看状态**: 在界面下方查看程序输出和运行状态
5. **停止程序**: 点击"停止程序"按钮终止运行
6. **开机启动**: 勾选复选框设置开机自动启动管理器

## 编译说明

使用Visual Studio 2019或更高版本：

```bash
# 还原NuGet包
dotnet restore

# 编译项目
dotnet build --configuration Release
```

## 注意事项

- 程序设置会自动保存到settings.txt文件
- 开机启动功能通过注册表实现，仅对当前用户有效
- 关闭管理器时如果目标程序仍在运行，会提示是否停止