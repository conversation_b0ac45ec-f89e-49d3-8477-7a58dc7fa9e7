# General
*.log
*.tmp

# Directories
/dist/
/build/
/node_modules/
/.cache/

# IDEs
*.iml
*.ipr
*.iws
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
.vscode/
*.log

# OS
.DS_Store
Thumbs.db

# Compiled
*.class
*.dll
*.exe
*.o
*.so

# Misc
*.swp
*.swo
*.bak
*.old
*~
__MACOSX/
*.out

# Rider
*.idea/**
**/out/**
**/build/**

# .NET/C# Build artifacts
bin/
obj/
[Dd]ebug/
[Rr]elease/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio temporary files
.vs/
*.user
*.userosscache
*.sln.docstates

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# NuGet
*.nupkg
*.snupkg
**/packages/*
!**/packages/build/
.nuget/

# MSBuild Binary and Structured Log
*.binlog

# Files built by Visual Studio
*.pdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp_proj
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Click-Once directory
publish/

# Publish Web Output
*.[Pp]ublish.xml
*.azurePubxml
*.pubxml
*.publishproj

# Windows Store app package directories and files
AppPackages/
BundleArtifacts/
Package.StoreAssociation.xml
_pkginfo.txt
*.appx
*.appxbundle
*.appxupload
